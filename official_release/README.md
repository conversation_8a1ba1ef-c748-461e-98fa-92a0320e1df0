# Lanemap Jenkins Pipeline

这是一个用于自动化地图（Lanemap）构建、检查和发布的Jenkins Pipeline项目。

## 概述

该Pipeline自动化处理以下核心流程：
- 地图编译和压缩
- FSD（Full Self-Driving）模块构建
- 路径检查和定位地图验证
- 自动化报告生成
- 地图文件打包和上传

## 主要功能

### 1. 代码管理
- 支持多项目SHA切换（通过`PROJECT_LIST`参数）
- 自动克隆相关Git仓库
- Apollo项目的repo同步

### 2. 构建流程
- **点云压缩与更新**：处理定位地图的点云数据
- **FSD构建**：使用Docker容器构建FSD相关模块
- **地图编译**：自动化地图数据编译流程

### 3. 质量检查
- **路径检查**：验证深圳和上海的导航路径
- **定位地图检查**：提交完整的地图检查任务
- **行为地图更新**：处理行为相关的地图数据

### 4. 发布管理
- 支持补丁发布（patch_opt）和完整发布
- 多城市地图压缩（深圳、上海、保定、长春、无锡、武汉、北京）
- 自动上传到数据中心
- 文件大小计算和优化

## 支持的城市

- 深圳 (Shenzhen)
- 上海 (Shanghai)  
- 保定 (Baoding)
- 长春 (Changchun)
- 无锡 (Wuxi)
- 武汉 (Wuhan)
- 北京 (Beijing)

## Pipeline参数

### 必需参数
- `lanemap_version`: 地图版本号
- `last_version`: 上一个版本号
- `city_names`: 城市名称列表

### 路径配置
- `road_root`: 道路数据根目录
- `buffer_root`: 缓冲区数据根目录
- `compile_input_root`: 编译输入根目录
- `lanemap_root`: 地图数据根目录
- `release_root`: 发布根目录
- `report_root`: 报告根目录

### 可选参数
- `PROJECT_LIST`: 项目列表（格式：项目名:SHA值）
- `patch_opt`: 是否为补丁发布
- `need_release_loc_map`: 是否需要发布定位地图
- `loc_map_thread_num`: 定位地图处理线程数

## Pipeline阶段

1. **节点分配 (Assign Node)**
   - 确定构建用户信息

2. **代码克隆 (Clone Git Repository)**
   - 克隆所有相关仓库
   - 初始化Apollo项目

3. **SHA切换 (Checkout FSD to new commit)**
   - 根据PROJECT_LIST切换到指定提交

4. **并行处理 (PtCloud Compress & FSD)**
   - 点云更新和压缩
   - FSD模块构建

5. **地图编译 (Lanemap Compile)**
   - 生成编译输入
   - 执行地图编译

6. **路径检查 (Route Check)**
   - 验证导航路径有效性

7. **定位地图检查 (LocMap Check)**
   - 提交完整地图检查任务

8. **地图压缩 (Lanemap Compress)**
   - 压缩编译后的地图数据

9. **行为地图更新 (Behavior Map Update)**
   - 更新行为相关地图数据

10. **发布报告 (Release Report Content)**
    - 生成发布报告内容

11. **打包上传 (Compress Release Zip & Upload Datahub)**
    - 压缩各城市地图文件
    - 上传到数据中心

## Docker环境

Pipeline使用以下Docker镜像：
- Apollo构建：`${X86_IMAGE}` (环境变量配置)
- 地图检查：`************:5000/library/apollo:dev-x86_64-18.04-20250219_rs`

## 缓存配置

- 启用远程缓存：`ENABLE_REMOTE_CACHE=1`
- 缓存服务器：`http://*************:9090`

## 通知系统

Pipeline集成飞书通知功能：
- 构建成功通知指定用户和群组
- 构建失败时发送错误通知

## 注意事项

1. **补丁发布**：当`patch_opt`为true时，会跳过部分检查步骤以加快发布速度
2. **线程配置**：根据系统资源调整`loc_map_thread_num`参数
3. **存储要求**：确保有足够的磁盘空间用于地图数据处理
4. **网络要求**：需要访问GitLab和Docker仓库的网络权限
