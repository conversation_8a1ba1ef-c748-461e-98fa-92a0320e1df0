def checkout_sha() {
    array = params.PROJECT_LIST.split(',')
    for (item in array) {
        p_name = item.split(':')[0]
        p_sha = item.split(':')[1]
        dir('apollo') {
            def change_path = sh returnStdout: true, script: "repo list -p ${p_name}"
            def rs_path = change_path.trim()
            sh "cd ${rs_path} && git fetch --unshallow && git checkout ${p_sha}"
        }
    }
}


// 子模块构建
def sub_build() {
    def rs_user_name = sh returnStdout: true, script: "id -u -n"
    def rs_user = rs_user_name.trim()
    def container = docker.image("${env.APOLLO_IMAGE}")
    container.inside("-v /etc/localtime:/etc/localtime:ro -v /etc/passwd:/etc/passwd:ro -v /etc/group:/etc/group:ro -u ${rs_user}  -v ${WORKSPACE}/apollo:/apollo  -v ${WORKSPACE}/ci:${WORKSPACE}/ci") {
        sh '''#!/bin/bash
set -e
cd /apollo
cp -fv ${WORKSPACE}/ci/apollo_base.sh ${WORKSPACE}/apollo/scripts
export ENABLE_REMOTE_CACHE=1
export REMOTE_CACHE_URL=http://*************:9090
bash apollo.sh build rs_localization
'''
    }
}

// 地图检查
def lanemap_check() {
    def rs_user_name = sh returnStdout: true, script: "id -u -n"
    def rs_user = rs_user_name.trim()
    def container = docker.image("************:5000/library/apollo:dev-x86_64-18.04-20250219_rs")
    container.inside("-v /etc/localtime:/etc/localtime:ro -v /etc/passwd:/etc/passwd:ro -v /etc/group:/etc/group:ro -u ${rs_user}  -v ${WORKSPACE}/apollo:/apollo -v /media/sti/lanemap:/media/sti/lanemap ") {
        sh """#!/bin/bash
        set -e
        cd /apollo
        pip install --no-index --find-links=modules/rs_localization/rs_map/lanemap/navigation/scripts/rstaxi_traj_processing/packages -r modules/rs_localization/rs_map/lanemap/navigation/scripts/rstaxi_traj_processing/requirements.txt
        python3 modules/rs_localization/rs_map/lanemap/navigation/scripts/rstaxi_traj_processing/lanemap_process/preprocess_lanemap.py --lanemap_version ${params.lanemap_version}
        python3 modules/rs_localization/rs_map/lanemap/navigation/scripts/rstaxi_traj_processing/fsd_process/valid_in_use_task_traj.py --lanemap_version ${params.lanemap_version}
        """

        if (params.patch_opt) {
            // 如果patch发版本，则不需要执行后续步骤
            echo "patch发版本，不需要全量检查。提前结束脚本"
            return
        } else {
            sh """#!/bin/bash
                set -e
                cd /apollo
                # rs taxi 线路检查，注意，此处区分了深圳和上海的路线，后续需要根据实际需求修改
                # patch发版本，不需要全量检查
                python3 modules/rs_localization/rs_map/lanemap/navigation/scripts/rstaxi_traj_processing/fsd_process/navi_traj_shortest.py --group_name group_shenzhen_all --city shenzhen --poi_file data/rstaxi_poi/shenzhen/rstaxi_shenzhen_all.geojson --lineName_file data/rstaxi_poi/shenzhen/rstaxi_shenzhen_all.txt --lanemap_version ${params.lanemap_version}
                python3 modules/rs_localization/rs_map/lanemap/navigation/scripts/rstaxi_traj_processing/fsd_process/navi_traj_shortest.py --group_name group_shanghai_all --city shanghai --poi_file data/rstaxi_poi/shanghai/rstaxi_shanghai_all.geojson --lineName_file data/rstaxi_poi/shanghai/rstaxi_shanghai_all.txt --lanemap_version ${params.lanemap_version}
            """
        }
    }
}

def send_feishu_message(release_type) {
    dir('script') {
        retry(5) {
            deleteDir()
            sh '<NAME_EMAIL>:xuantie/fsd_project/ci.git .'
        }
        if(release_type == "success") {
            sh 'python3 ${WORKSPACE}/script/tools/feishu_lanemap_message.py -t success --user ${RS_USERNAME} -g oc_2a639ba309c5a39a2aaf3a9298771491'
            sh 'python3 ${WORKSPACE}/script/tools/feishu_lanemap_message.py -t success --user Harvey.Shi'
        }else if(release_type == "failed") {
            sh 'python3 ${WORKSPACE}/script/tools/feishu_lanemap_message.py -t fail --user ${RS_USERNAME} -g oc_2a639ba309c5a39a2aaf3a9298771491' 
            sh 'python3 ${WORKSPACE}/script/tools/feishu_lanemap_message.py -t success --user Harvey.Shi'
        }
    } 
}

pipeline {
    agent { label 'lanemap' }
    options {
        ansiColor('xterm')
        timestamps()
    }
    environment {
        APOLLO_IMAGE="${X86_IMAGE}"
    }
    stages {
        stage('Assign Node') {
            steps {
                script {
                    if (currentBuild.rawBuild.getCause(hudson.model.Cause$UserIdCause)) {
                        env.RS_USERNAME = currentBuild.rawBuild.getCause(hudson.model.Cause$UserIdCause).getUserId()
                    }else {
                        env.RS_USERNAME = "timer"
                    }
                    currentBuild.description = "<b>BuildUser</b>:${RS_USERNAME}"
                }
            }
        }
        stage('Clone Git Repository') {
            steps {
                script {
                    cleanWs()
                    // 克隆指定分支的Git仓库
                    sh '<NAME_EMAIL>:harvey.shi/lanemap_release_input.git -b main --depth 1'
                    sh '<NAME_EMAIL>:harvey.shi/lanemap_release_loc_map.git -b main --depth 1'
                    sh '<NAME_EMAIL>:harvey.shi/lanemap_release_report.git -b main --depth 1 --recursive'
                    sh '<NAME_EMAIL>:xuantie/hyper_labeler/static3d/tool/lanemap_auto_checker.git -b dev_compile --depth 1'

                    dir("apollo") {
                        sh """repo init -u ${MANIFEST_URL} -b ${MANIFEST_BRANCH} --git-lfs --no-tags --depth=1 -m default.xml && repo sync -j 8
                        mkdir -p modules/rs_localization/rs_map/lanemap/navigation/scripts
                        cd modules/rs_localization/rs_map/lanemap/navigation/scripts
                        <NAME_EMAIL>:harvey.shi/rstaxi_traj_processing.git --recursive
                        """
                    }
                    dir('ci') {
                        sh 'git clone git@**********:platform/pkg_scripts.git -b ci .  --depth=1'
                        sh 'ls -al'
                        sh 'cd x86 && cat Dockerfile'
                        sh "cd x86 && sed -i 's/dev-x86_64-18.04-20241206_rs/dev-x86_64-18.04-20250219_rs/' Dockerfile"
                        sh 'cd x86 && cat Dockerfile'
                    }
                }
            }
        }
        stage('Checkout FSD to new commit') {
            when {
                //如project_list不为空则执行
                expression {params.PROJECT_LIST != ''}
            }
            steps {
                script {
                    checkout_sha()
                }
            }
        }
        stage('PtCloud Compress & FSD') {
            parallel {
                stage('# PtCloud Update & Compress') {
                    failFast true
                    steps {
                        script {
                            if (params.need_release_loc_map) {
                                dir('lanemap_release_input') {
                                    sh "python3 make_input_json.py \
                                        --mode loc_map  \
                                        --last_version ${params.last_version}  \
                                        --lanemap_version ${params.lanemap_version}  \
                                        --city_names ${params.city_names} \
                                        --road_root ${params.road_root} \
                                        --buffer_root ${params.buffer_root}  \
                                        --compile_input_root ${params.compile_input_root}  \
                                        --lanemap_root ${params.lanemap_root}  \
                                        --loc_map_path ${params.loc_map_path}  \
                                        --release_root ${params.release_root}  \
                                        --report_root ${params.report_root}  \
                                        --save_lanemap_path ${params.save_lanemap_path}  \
                                        --loc_map_steps 1 2 3  \
                                        --loc_map_thread_num ${params.loc_map_thread_num}"
                                }
                                dir('lanemap_release_loc_map') {
                                    sh "python3 scripts/pipeline_loc_map.py  \
                                        --mode loc_map  \
                                        --lanemap_version ${params.lanemap_version}  \
                                        --compile_input_root ${params.compile_input_root}"
                                }
                            }
                        }
                    }
                }
                stage('FSD Build') {
                    failFast true
                    steps {
                        script {
                            sub_build()
                        }
                    }
                }
            }
        }
        stage('# Lanemap Compile') {
            failFast true
            steps {
                script {
                    dir('lanemap_release_input') {
                        sh "python3 make_input_json.py \
                            --mode compile  \
                            --last_version ${params.last_version}  \
                            --lanemap_version ${params.lanemap_version}  \
                            --city_names ${params.city_names} \
                            --road_root ${params.road_root} \
                            --buffer_root ${params.buffer_root}  \
                            --compile_input_root ${params.compile_input_root}  \
                            --lanemap_root ${params.lanemap_root}  \
                            --loc_map_path ${params.loc_map_path}  \
                            --release_root ${params.release_root}  \
                            --report_root ${params.report_root}  \
                            --save_lanemap_path ${params.save_lanemap_path}  \
                            --loc_map_steps 4  \
                            --loc_map_thread_num ${params.loc_map_thread_num} \
                            --SAVE_LABEL_DATA"
                    }
                    dir('lanemap_auto_checker') {
                        sh "python3 compile_main.py  \
                            --mode compile  \
                            --compile_input_root ${params.compile_input_root}  \
                            --lanemap_version ${params.lanemap_version}"
                    }
                }
            }
        }
        stage('# Route Check') {
            failFast true
            steps {
                script {
                    lanemap_check()
                }
            }
        }
        stage('# LocMap Check') {
            failFast true
            steps {
                script {
                    if (params.need_release_loc_map) {
                        dir('lanemap_release_report') {
                            sh "python3 loc_map_check/submit_task_full.py ${params.lanemap_version}"
                        }
                    }
                }
            }
        }
        stage('# Lanemap Compress') {
            failFast true
            steps {
                script {
                    dir('lanemap_release_loc_map') {
                        sh "python3 scripts/pipeline_loc_map.py \
                            --mode compile  \
                            --compile_input_root ${params.compile_input_root}  \
                            --lanemap_version ${params.lanemap_version}"
                    }
                }
            }
        }
        stage('# Behavior Map Update') {
            failFast true
            steps {
                script {
                    dir('lanemap_release_report') {
                        sh "python3 scripts/pipeline_report.py --mode compile \
                            --compile_input_root ${params.compile_input_root} \
                            --lanemap_version ${params.lanemap_version}"
                    }
                }
            }
        }
        stage('# Release Report Content') {
            failFast true
            steps {
                script {
                    if (params.patch_opt) {
                        dir('lanemap_release_input') {
                            sh "python3 make_input_json.py --mode report \
                            --last_version ${params.last_version} \
                            --lanemap_version ${params.lanemap_version} \
                            --report_steps 2 3"
                        }
                    } else {
                        dir('lanemap_release_input') {
                            sh "python3 make_input_json.py --mode report \
                            --last_version ${params.last_version} \
                            --lanemap_version ${params.lanemap_version}"
                        }
                    }
                    dir('lanemap_release_report') {
                        sh "python3 scripts/pipeline_report.py \
                        --mode report \
                        --compile_input_root ${params.compile_input_root} \
                        --lanemap_version ${params.lanemap_version}"
                    }
                }
            }
        }
        stage('# Compress Release Zip & Upload Datahub') {
            failFast true
            steps {
                script {
                    echo "=========================================="
                    echo "开始压缩地图文件并上传到数据中心"
                    echo "地图版本: ${params.lanemap_version}"
                    echo "补丁模式: ${params.patch_opt}"
                    echo "缓冲区根目录: ${params.buffer_root}"
                    echo "输出目录: ${WORKSPACE}/lanemap_release_report_files"
                    echo "=========================================="
                    
                    def patch_opt = ""
                    if (params.patch_opt) {
                        patch_opt = "--only_lanemap 1"
                        echo "✓ 启用补丁模式，只处理地图文件"
                    } else {
                        echo "✓ 完整发布模式，处理所有文件"
                    }
                    
                    echo "创建输出目录..."
                    sh "mkdir -p ${WORKSPACE}/lanemap_release_report_files"
                    sh "ls -la ${WORKSPACE}/lanemap_release_report_files"
                    
                    dir('lanemap_release_report') {
                        echo "进入地图发布报告目录: \$(pwd)"
                        
                        def startTime = System.currentTimeMillis()
                        echo "开始并行压缩各城市地图文件..."
                        echo "支持的城市: 深圳、上海、保定、长春、无锡、武汉、北京"
                        
                        // 并行压缩各城市地图
                        parallel (
                            '深圳': {
                                echo "🏙️ [深圳] 开始压缩地图文件..."
                                def cityStartTime = System.currentTimeMillis()
                                sh "python3 scripts/compress_map.py ${patch_opt} --v ${params.lanemap_version} --city shenzhen --i ${params.buffer_root} --o ${WORKSPACE}/lanemap_release_report_files"
                                def cityEndTime = System.currentTimeMillis()
                                echo "✅ [深圳] 压缩完成，耗时: ${(cityEndTime - cityStartTime)/1000}秒"
                            },
                            '上海': {
                                echo "🏙️ [上海] 开始压缩地图文件..."
                                def cityStartTime = System.currentTimeMillis()
                                sh "python3 scripts/compress_map.py ${patch_opt} --v ${params.lanemap_version} --city shanghai --i ${params.buffer_root} --o ${WORKSPACE}/lanemap_release_report_files"
                                def cityEndTime = System.currentTimeMillis()
                                echo "✅ [上海] 压缩完成，耗时: ${(cityEndTime - cityStartTime)/1000}秒"
                            },
                            '保定': {
                                echo "🏙️ [保定] 开始压缩地图文件..."
                                def cityStartTime = System.currentTimeMillis()
                                sh "python3 scripts/compress_map.py ${patch_opt} --v ${params.lanemap_version} --city baoding --i ${params.buffer_root} --o ${WORKSPACE}/lanemap_release_report_files"
                                def cityEndTime = System.currentTimeMillis()
                                echo "✅ [保定] 压缩完成，耗时: ${(cityEndTime - cityStartTime)/1000}秒"
                            },
                            '长春': {
                                echo "🏙️ [长春] 开始压缩地图文件..."
                                def cityStartTime = System.currentTimeMillis()
                                sh "python3 scripts/compress_map.py ${patch_opt} --v ${params.lanemap_version} --city changchun --i ${params.buffer_root} --o ${WORKSPACE}/lanemap_release_report_files"
                                def cityEndTime = System.currentTimeMillis()
                                echo "✅ [长春] 压缩完成，耗时: ${(cityEndTime - cityStartTime)/1000}秒"
                            },
                            '无锡': {
                                echo "🏙️ [无锡] 开始压缩地图文件..."
                                def cityStartTime = System.currentTimeMillis()
                                sh "python3 scripts/compress_map.py ${patch_opt} --v ${params.lanemap_version} --city wuxi --i ${params.buffer_root} --o ${WORKSPACE}/lanemap_release_report_files"
                                def cityEndTime = System.currentTimeMillis()
                                echo "✅ [无锡] 压缩完成，耗时: ${(cityEndTime - cityStartTime)/1000}秒"
                            },
                            '武汉': {
                                echo "🏙️ [武汉] 开始压缩地图文件..."
                                def cityStartTime = System.currentTimeMillis()
                                sh "python3 scripts/compress_map.py ${patch_opt} --v ${params.lanemap_version} --city wuhan --i ${params.buffer_root} --o ${WORKSPACE}/lanemap_release_report_files"
                                def cityEndTime = System.currentTimeMillis()
                                echo "✅ [武汉] 压缩完成，耗时: ${(cityEndTime - cityStartTime)/1000}秒"
                            },
                            '北京': {
                                echo "🏙️ [北京] 开始压缩地图文件..."
                                def cityStartTime = System.currentTimeMillis()
                                sh "python3 scripts/compress_map.py ${patch_opt} --v ${params.lanemap_version} --city beijing --i ${params.buffer_root} --o ${WORKSPACE}/lanemap_release_report_files"
                                def cityEndTime = System.currentTimeMillis()
                                echo "✅ [北京] 压缩完成，耗时: ${(cityEndTime - cityStartTime)/1000}秒"
                            }
                        )
                        
                        def endTime = System.currentTimeMillis()
                        echo "🎉 所有城市地图压缩完成！总耗时: ${(endTime - startTime)/1000}秒"
                        
                        echo "检查生成的文件..."
                        sh "ls -lah ${WORKSPACE}/lanemap_release_report_files/"

                        // 上传和文件大小计算仍需顺序执行
                        echo "=========================================="
                        echo "开始上传地图文件到数据中心..."
                        def uploadStartTime = System.currentTimeMillis()
                        sh "python3 scripts/upload_fsd_pkg_lanemap.py --root ${WORKSPACE}/lanemap_release_report_files --lanemap_version ${params.lanemap_version}"
                        def uploadEndTime = System.currentTimeMillis()
                        echo "✅ 文件上传完成，耗时: ${(uploadEndTime - uploadStartTime)/1000}秒"
                        
                        echo "开始计算在线更新文件大小..."
                        def calcStartTime = System.currentTimeMillis()
                        sh "python3 scripts/cal_online_update_filesize.py --lanemap_version ${params.lanemap_version}"
                        def calcEndTime = System.currentTimeMillis()
                        echo "✅ 文件大小计算完成，耗时: ${(calcEndTime - calcStartTime)/1000}秒"
                        
                        echo "=========================================="
                        echo "🎊 地图压缩和上传阶段全部完成！"
                        echo "=========================================="
                    }
                }
            }
        }
    }
    post {
        success {
            node('ci') {
                script {
                    send_feishu_message("success")
                }
            }
        }
        failure {
            node('ci') {
                script {
                    send_feishu_message("failed")
                }
            }
        }
    }
}
