def send_feishu_message(release_type) {
    dir('script') {
        retry(5) {
            deleteDir()
            sh '<NAME_EMAIL>:xuantie/fsd_project/ci.git .'
        }
        if(release_type == "success") {
            sh 'python3 ${WORKSPACE}/script/tools/feishu_lanemap_message.py -t success --user ${RS_USERNAME} -g oc_2a639ba309c5a39a2aaf3a9298771491'
            sh 'python3 ${WORKSPACE}/script/tools/feishu_lanemap_message.py -t success --user forward.zhang'
        }else if(release_type == "failed") {
            sh 'python3 ${WORKSPACE}/script/tools/feishu_lanemap_message.py -t fail --user ${RS_USERNAME} -g oc_2a639ba309c5a39a2aaf3a9298771491'
            sh 'python3 ${WORKSPACE}/script/tools/feishu_lanemap_message.py -t fail --user forward.zhang'
        }
    } 
}

pipeline {
    agent { label 'lanemap' }
    options {
        ansiColor('xterm')
        timestamps()
    }
    stages {
        stage('Assign Node') {
            steps {
                script {
                    if (currentBuild.rawBuild.getCause(hudson.model.Cause$UserIdCause)) {
                        env.RS_USERNAME = currentBuild.rawBuild.getCause(hudson.model.Cause$UserIdCause).getUserId()
                    }else {
                        env.RS_USERNAME = "timer"
                    }
                    currentBuild.description = "<b>BuildUser</b>:${RS_USERNAME}"
                }
            }
        }
        stage('Clone Git Repository') {
            steps {
                script {
                    cleanWs()
                    // 克隆指定分支的Git仓库
                    sh '<NAME_EMAIL>:harvey.shi/lanemap_release_input.git -b main --depth 1'
                    sh '<NAME_EMAIL>:harvey.shi/lanemap_release_loc_map.git -b main --depth 1'
                    sh '<NAME_EMAIL>:xuantie/hyper_labeler/static3d/tool/lanemap_auto_checker.git -b dev_compile --depth 1'
                    sh '<NAME_EMAIL>:harvey.shi/lanemap_release_report.git -b main --depth 1'
                }
            }
        }
        stage('# Release Loc Map') {
            steps {
                script {
                    dir('lanemap_release_input') {
                        sh "python3 make_input_json.py \
                            --mode loc_map  \
                            --last_version ${params.last_version}  \
                            --lanemap_version ${params.lanemap_version}  \
                            --city_names ${params.city_names} \
                            --road_root ${params.road_root} \
                            --buffer_root ${params.buffer_root}  \
                            --compile_input_root ${params.compile_input_root}  \
                            --lanemap_root ${params.lanemap_root}  \
                            --loc_map_path ${params.loc_map_path}  \
                            --release_root ${params.release_root}  \
                            --report_root ${params.report_root}  \
                            --save_lanemap_path ${params.save_lanemap_path}  \
                            --loc_map_steps 1 2 3  \
                            --loc_map_thread_num 10"
                    }
                }
                script {
                    dir('lanemap_release_loc_map') {
                        sh "python3 scripts/pipeline_loc_map.py  \
                            --mode loc_map  \
                            --lanemap_version ${params.lanemap_version}  \
                            --compile_input_root ${params.compile_input_root}"
                    }
                }
            }
        }
        stage('# Lanemap Compile') {
            failFast true
            steps {
                script {
                    dir('lanemap_release_input') {
                        sh "python3 make_input_json.py \
                            --mode compile  \
                            --last_version ${params.last_version}  \
                            --lanemap_version ${params.lanemap_version}  \
                            --city_names ${params.city_names} \
                            --road_root ${params.road_root} \
                            --buffer_root ${params.buffer_root}  \
                            --compile_input_root ${params.compile_input_root}  \
                            --lanemap_root ${params.lanemap_root}  \
                            --loc_map_path ${params.loc_map_path}  \
                            --release_root ${params.release_root}  \
                            --report_root ${params.report_root}  \
                            --save_lanemap_path ${params.save_lanemap_path}  \
                            --loc_map_steps 4  \
                            --loc_map_thread_num ${params.loc_map_thread_num} \
                            --SAVE_LABEL_DATA"
                    }
                    dir('lanemap_auto_checker') {
                        sh "python3 compile_main.py  \
                            --mode compile  \
                            --compile_input_root ${params.compile_input_root}  \
                            --lanemap_version ${params.lanemap_version}"
                    }
                }
            }
        }
        stage('# LocMap Check') {
            failFast true
            steps {
                script {
                    dir('lanemap_release_report') {
                        sh "python3 loc_map_check/submit_task_full.py ${params.lanemap_version}"
                    }
                }
            }
        }
    }
    post {
        success {
            node('ci') {
                script {
                    cleanWs()
                    send_feishu_message("success")
                }
            }
        }
        failure {
            node('ci') {
                script {
                    cleanWs()
                    send_feishu_message("failed")
                }
            }
        }
    }
}
